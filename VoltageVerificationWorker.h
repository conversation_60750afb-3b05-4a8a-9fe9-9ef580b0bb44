#ifndef VOLTAGEVERIFICATIONWORKER_H
#define VOLTAGEVERIFICATIONWORKER_H

#include <QObject>
#include <QThread>
#include <QMessageBox>
#include <QDebug>
#include <QTimer>
#include <QVector>
#include "WorkerConfig.h"

class VoltageVerificationWorker : public QObject
{
    Q_OBJECT

public:
    explicit VoltageVerificationWorker(QObject *parent = nullptr);
    ~VoltageVerificationWorker();

    void setDeviceConfig(const AdjDeviceConfig &config);
    void requestAbort();

    // 设置命令发送函数指针
    typedef QPair<bool, QString> (*CommandHandlerFunc)(const QByteArray &, const QString &);
    void setCommandHandlers(CommandHandlerFunc adjHandler);

    // 获取当前校准状态
    int getCurrentLevel() const { return m_currentLevel; }
    int getCurrentChannel() const { return m_currentChannel; }

public slots:
    void startVoltageVerification();
    void restartVoltageVerification(int levelIndex, double referenceVoltage, int channel);
    void onUserPromptResult(bool confirmed);

signals:
    void logMessage(const QString &message);
    void voltageVerificationFinished(bool success);
    void voltageVerificationProgress(int currentStep, int totalSteps);
    void updateVoltageChannelData(double referenceVoltage, int channel,
                                  QVector<double> voltages, double measuredVoltage,
                                  double deviation, double allowedDeviation, bool verificationResult);
    void requestUserPrompt(const QString &title, const QString &message);
    void voltageVerificationStarted();

private slots:
    void processCollectedData();

private:
    struct VoltageLevel
    {
        double targetVoltage; // 目标电压值 (mV)
        QString description;  // 描述文本
    };

    AdjDeviceConfig m_deviceConfig;
    bool m_abortRequested;
    bool m_isRecalibrationMode;
    QTimer *m_dataCollectionTimer;
    QTimer *m_stabilizationTimer;

    // 命令处理函数指针
    CommandHandlerFunc m_adjCommandHandler;

    // 校准状态
    int m_currentLevel;                // 当前校准档位 (0-4: -10mV, 10mV, 30mV, 50mV, 75mV)
    int m_currentChannel;              // 当前校准通道 (0-7: CH1-CH8)
    int m_currentReading;              // 当前读取次数 (0-3: 4次读取)
    QVector<double> m_voltageReadings; // 当前通道当前档位的4次电压读取值

    // 5个电压档位定义
    static const QVector<VoltageLevel> VOLTAGE_LEVELS;
    static const double VOLTAGE_COLLECTION_INTERVAL; // 数据采集间隔 (ms)
    static const double STABILIZATION_TIME;          // 稳定时间 (ms)

    // 私有方法
    void showUserPrompt(double targetVoltage, int channel);
    void startStabilizationTimer();
    void startDataCollection();
    void moveToNextChannel();
    void moveToNextLevel();
    void moveToNextLevelWithoutProgress();
    void finishVerification();

    // 设备通信相关 - 电压校准专用
    double read_voltage(int channel);
    QPair<bool, QVector<double>> read_voltages(int channel, int attempt);
    QPair<bool, QString> sendCommand(const QByteArray &command, const QString &commandType);
    QByteArray createModbusCommand(const QString &hexCommand);
    uint16_t calculateCRC16(const QByteArray &data);
    QByteArray createVoltageReadFrame(uint8_t deviceAddr, uint16_t baseAddr, int channel);

    // TCP协议支持函数
    bool isTcpDevice(const QString &deviceModel);
    QByteArray createModbusTcpFrame(const QByteArray &rtuData);
    QByteArray createModbusRtuFrame(const QString &hexCommand);
    bool interruptibleSleep(int seconds);
    bool interruptibleMSleep(int milliseconds);

    // 数据处理
    double calculateAllowedDeviation(double measuredVoltage) const;
    bool isWithinTolerance(double deviation, double allowedDeviation) const;
};

#endif // VOLTAGEVERIFICATIONWORKER_H
