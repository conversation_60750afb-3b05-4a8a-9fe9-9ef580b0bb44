#include "CalibrationDialog.h"
#include "ui_CalibrationDialog.h"
#include <QDebug>
#include <QMessageBox>
#include <QScreen>

CalibrationDialog::CalibrationDialog(QWidget *parent) : QDialog(parent),
                                                        ui(new Ui::CalibrationDialog)
{
    ui->setupUi(this);

    // // 设置最小和最大尺寸
    // //this->setMinimumSize(300, 200);
    // //this->setMaximumSize(800, 600);

    // // 调整窗口大小以适应内容
    // this->adjustSize();

    QScreen *screen = QApplication::primaryScreen();
    QRect screenGeometry = screen->geometry();
    int width = screenGeometry.width() * 0.7;
    int height = screenGeometry.height() * 0.7;

    // Set minimum and fixed sizes to ensure the dialog is actually 80% of screen size
    setMinimumSize(width, height);
    resize(width, height);

    // 设置窗口标题
    setWindowTitle("标定");

    // 移除帮助按钮
    setWindowFlags(windowFlags() & ~Qt::WindowContextHelpButtonHint);

    // 允许最小化
    // setWindowFlags(windowFlags() | Qt::WindowMinimizeButtonHint);

    setWindowFlags(windowFlags() | Qt::WindowSystemMenuHint | Qt::WindowMaximizeButtonHint);

    QStringList boardCardModels = {"1618A-N18P-0B05", "1618A-N18P-0B06", "1618A-N19P-0B05",
                                   "1618A-N19P-0B06", "1618A-N18P-1B05", "1618A-N18P-1B06",
                                   "1618A-N19P-1B05", "1618A-N19P-1B06", "1618A-N18T",
                                   "1618A-N19T", "1618A-N18D", "1618A-N19D",
                                   "1618A-R18P-0B05", "1618A-R18P-0B06", "1618A-R19P-0B05",
                                   "1618A-R19P-0B06", "1618A-R18P-1B05", "1618A-R18P-1B06",
                                   "1618A-R19P-1B05", "1618A-R19P-1B06", "1618A-R18T",
                                   "1618A-R19T", "1618A-R18D", "1618A-R19D",
                                   "1618A-T18T-T", "1618A-T18T-K", "1618A-T18M-T", "1618A-T18M-K"};

    QStringList rtdModels;
    // for (const QString &model : boardCardModels) {
    //     QString rtdModel = model;       // 复制一份可修改的字符串
    //     rtdModel.replace("-N", "-R");  // 修改临时变量
    //     rtdModels.append(rtdModel);
    // }

    QStringList allModels = boardCardModels + rtdModels;
    ui->boardCardModel_Combo_CalDialog->addItems(allModels);

    // checkBox互斥
    checkBoxGroup_CalDialog = new QButtonGroup(this);
    checkBoxGroup_CalDialog->addButton(ui->serialNum_CalDialog, 1);
    checkBoxGroup_CalDialog->addButton(ui->systemTime_CalDialog, 2);
    checkBoxGroup_CalDialog->addButton(ui->filterFrequency_CalDialog, 3);
    checkBoxGroup_CalDialog->addButton(ui->baudRate_CalDialog, 4);
    checkBoxGroup_CalDialog->addButton(ui->deviceAddr_CalDialog, 5);
    checkBoxGroup_CalDialog->addButton(ui->boardCardNumber_CalDialog, 6);
    checkBoxGroup_CalDialog->addButton(ui->boardCardModel_CalDialog, 7);
    checkBoxGroup_CalDialog->addButton(ui->filterFrequency_CalDialog_1618A, 8);

    // 设置互斥
    checkBoxGroup_CalDialog->setExclusive(true);
    // connect(checkBoxGroup_CalDialog, QOverload<int, bool>::of(&QButtonGroup::buttonToggled), this, &CalibrationDialog::onCheckBoxToggled);

    ui->abortButton->setEnabled(false);
    ui->abortButton_2->setEnabled(false);

    QList<QComboBox *> comboBoxes = this->findChildren<QComboBox *>();
    for (QComboBox *box : comboBoxes)
    {
        box->setMaxVisibleItems(6);
    }

    // 初始化电压校准模式标志
    isVoltageCalibrationMode = false;

    // 默认隐藏电压校准组件
    ui->groupBox_5->setVisible(false);
}

void CalibrationDialog::showEvent(QShowEvent *event)
{
    // 先调用基类的 showEvent，确保正常显示流程
    QDialog::showEvent(event);

    // 将对话框居中于屏幕可用区域
    QScreen *screen = QApplication::primaryScreen();
    QRect availableRect = screen->availableGeometry(); // 获取屏幕可用区域
    QPoint center = availableRect.center();            // 获取屏幕中心的坐标
    int x = center.x() - this->width() / 2;            // 计算对话框左上角的 x 坐标
    int y = center.y() - this->height() / 2;           // 计算对话框左上角的 y 坐标
    this->move(x, y);                                  // 移动对话框到计算出的位置
}

CalibrationDialog::~CalibrationDialog()
{
    delete checkBoxGroup_CalDialog;
    delete ui;
}

void CalibrationDialog::updateButtonStates(const QString &text)
{
    // 首先恢复所有组件到默认启用状态
    ui->serialNum_CalDialog->setEnabled(true);
    ui->filterFrequency_CalDialog->setEnabled(true);
    ui->baudRate_CalDialog->setEnabled(true);

    // 复位
    ui->reset_CalDialog->setEnabled(true);

    // 透传模式按钮默认禁用
    ui->enterTransparentMode->setEnabled(false);
    ui->quitTransparentMode->setEnabled(false);

    ui->systemTime_CalDialog->setEnabled(false);
    ui->deviceAddr_CalDialog->setEnabled(false);
    ui->boardCardNumber_CalDialog->setEnabled(false);
    ui->boardCardModel_CalDialog->setEnabled(false);
    ui->boardCardModel_Combo_CalDialog->setEnabled(false);
    ui->boardCardModel_Label_CalDialog->setEnabled(false);

    // 1618A 滤波控件默认禁用
    ui->filterFrequency_CalDialog_1618A->setEnabled(false);

    // 根据设备类型进行特殊设置
    if (text == "1611A")
    {
        // 1611A设备：所有组件都保持启用状态（已在上面设置）
        // 隐藏板卡相关组件
        // ui->boardCardNumber_CalDialog->setEnabled(false);
        // ui->boardCardModel_CalDialog->setEnabled(false);
        // ui->boardCardModel_Combo_CalDialog->setEnabled(false);
        // ui->boardCardModel_Label_CalDialog->setEnabled(false);
    }
    else if (text == "1611A-HT(PT100)" || text == "1611A-HT(PT1000)")
    {
        // 1611A透传模式设备：启用透传模式按钮，隐藏板卡相关组件
        ui->enterTransparentMode->setEnabled(true);
        ui->quitTransparentMode->setEnabled(true);
        ui->reset_CalDialog->setEnabled(false);    // 1611A-HT无复位
        ui->baudRate_CalDialog->setEnabled(false); // 1611A-HT透传模式不需要波特率设置
    }
    else if (text == "1611A-RTD")
    {
        // 1611A-RTD透传模式设备：启用透传模式按钮
        ui->enterTransparentMode->setEnabled(true);
        ui->quitTransparentMode->setEnabled(true);
        // 1611A-RTD有复位功能，保持复位按钮启用
        // 1611A-RTD需要波特率设置，保持波特率按钮启用
    }
    else if (text == "1618A-L")
    {
        // 1618A-L透传模式设备：启用透传模式按钮和板卡相关组件
        ui->enterTransparentMode->setEnabled(true);
        ui->quitTransparentMode->setEnabled(true);
        ui->baudRate_CalDialog->setEnabled(false);             // 1618A-L透传模式不需要波特率设置
        ui->filterFrequency_CalDialog->setEnabled(false);      // 无设备滤波次数 但是板卡有
        ui->filterFrequency_CalDialog_1618A->setEnabled(true); // 启用1618A专用滤波次数（需要透传模式检查）
        ui->boardCardNumber_CalDialog->setEnabled(true);
        ui->boardCardModel_CalDialog->setEnabled(true);
        ui->boardCardModel_Combo_CalDialog->setEnabled(true);
        ui->boardCardModel_Label_CalDialog->setEnabled(true);
        // 板卡相关组件保持启用
    }
    else if (text == "1618A-N")
    {
        // 1618A-N设备：禁用系统时间、设备地址、设备滤波频率，启用板卡滤波频率
        ui->filterFrequency_CalDialog->setEnabled(false);      // 无设备滤波次数 但是板卡有
        ui->filterFrequency_CalDialog_1618A->setEnabled(true); // 启用1618A专用滤波次数
        ui->boardCardNumber_CalDialog->setEnabled(true);
        ui->boardCardModel_CalDialog->setEnabled(true);
        ui->boardCardModel_Combo_CalDialog->setEnabled(true);
        ui->boardCardModel_Label_CalDialog->setEnabled(true);
        // 板卡相关组件保持启用
    }
    else
    {
        // 其他设备：禁用特定组件
        ui->boardCardNumber_CalDialog->setEnabled(false);
        ui->boardCardModel_CalDialog->setEnabled(false);
        ui->boardCardModel_Combo_CalDialog->setEnabled(false);
        ui->boardCardModel_Label_CalDialog->setEnabled(false);
        // 保持序列号、滤波频率、波特率启用
    }
}

void CalibrationDialog::updateCalibrationButtonStates(const QString &cardType)
{
    // 检查是否为TC类型
    if (cardType.startsWith("TC-"))
    {
        // TC类型：启用电压校准，禁用电阻校准
        isVoltageCalibrationMode = true;
        ui->startCal_CalDialog->setEnabled(false);
        ui->startCal_CalDialog_2->setEnabled(true);
        ui->groupBox_5->setVisible(true);  // 显示电压校准组件
        ui->groupBox_2->setVisible(false); // 隐藏电阻校准组件

        qDebug() << "检测到TC类型设备，启用电压校准模式";
    }
    else
    {
        // 非TC类型：启用电阻校准，禁用电压校准
        isVoltageCalibrationMode = false;
        ui->startCal_CalDialog->setEnabled(true);
        ui->startCal_CalDialog_2->setEnabled(false);
        ui->groupBox_5->setVisible(false); // 隐藏电压校准组件
        ui->groupBox_2->setVisible(true);  // 显示电阻校准组件

        qDebug() << "检测到非TC类型设备，启用电阻校准模式";
    }
}

void CalibrationDialog::resetToResistanceCalibrationMode()
{
    // 重置校准模式标志
    isVoltageCalibrationMode = false;

    // 恢复按钮状态：启用电阻校准，禁用电压校准
    ui->startCal_CalDialog->setEnabled(true);
    ui->startCal_CalDialog_2->setEnabled(false);

    // 显示电阻校准组件，隐藏电压校准组件
    ui->groupBox_2->setVisible(true);  // 电阻校准组件
    ui->groupBox_5->setVisible(false); // 电压校准组件

    // 清空电压校准表格
    if (ui->readDataTable_2)
    {
        ui->readDataTable_2->clear();
        ui->readDataTable_2->setRowCount(0);
    }

    // 清空电阻校准表格
    if (ui->readDataTable)
    {
        ui->readDataTable->clear();
        ui->readDataTable->setRowCount(0);
    }

    // 清空板卡信息
    ui->boardCardModelEdit_CalDialog->clear();
    ui->boardCardNumberEdit_CalDialog->clear();

    qDebug() << "校准对话框已重置为电阻校准模式";
}

void CalibrationDialog::on_readBtn_CalDialog_clicked()
{
    int checkedId = checkBoxGroup_CalDialog->checkedId();
    switch (checkedId)
    {
    case 1:
        qDebug() << "Serial Number checkbox is checked";
        emit signal_readCommand_CalDialog(ui->deviceModel_CalDialog->text(), "readNum");
        break;
    case 2:
        qDebug() << "System Time checkbox is checked";
        break;
    case 3:
        qDebug() << "Filter Frequency checkbox is checked";
        emit signal_readCommand_CalDialog(ui->deviceModel_CalDialog->text(), "readFilterNum");
        break;
    case 4:
        qDebug() << "Baud Rate checkbox is checked";
        emit signal_readCommand_CalDialog(ui->deviceModel_CalDialog->text(), "readBaudRate");
        break;
    case 5:
        qDebug() << "Device Address checkbox is checked";
        break;
    case 6:
        emit signal_readCommand_CalDialog(ui->deviceModel_CalDialog->text(), "readBoardCardNumber");
        break;
    case 7:
        emit signal_readCommand_CalDialog(ui->deviceModel_CalDialog->text(), "readBoardCardModel");
        break;
    case 8:
        qDebug() << "1618A Filter Frequency checkbox is checked";
        emit signal_readCommand_CalDialog(ui->deviceModel_CalDialog->text(), "readFilterNum1618A");
        break;
    default:
        qDebug() << "No checkbox is checked";
        QMessageBox::warning(this, "提示", "请勾选至少一个系统参数");
        break;
    }
}

void CalibrationDialog::on_writeBtn_CalDialog_clicked()
{
    int checkedId = checkBoxGroup_CalDialog->checkedId();
    switch (checkedId)
    {
    case 1:
        qDebug() << "Serial Number checkbox is checked";
        // 检查输入是否为空且长度必须为10个字符
        if (ui->serialNumEdit_Cal->text().isEmpty())
        {
            QMessageBox::warning(this, "输入错误", "序列号输入不能为空，请输入有效的序列号。");
        }
        else if (ui->serialNumEdit_Cal->text().length() != 10)
        {
            QMessageBox::warning(this, "输入错误", "序列号必须为10个字符。");
        }
        else
        {
            emit signal_writeCommand_CalDialog(ui->deviceModel_CalDialog->text(), "writeNum", ui->serialNumEdit_Cal->text()); // 设备型号、功能码、写入值
        }
        break;
    case 2:
        qDebug() << "System Time checkbox is checked";
        break;
    case 3:
        qDebug() << "Filter Frequency checkbox is checked";
        if (ui->filterNum_CalDialog->text().isEmpty())
        {
            QMessageBox::warning(this, "输入错误", "滤波次数输入不能为空，请输入有效的滤波次数。");
        }
        else
        {
            emit signal_writeCommand_CalDialog(ui->deviceModel_CalDialog->text(), "writeFilterNum", ui->filterNum_CalDialog->text()); // 设备型号、功能码、写入值
        }
        break;
    case 4:
        qDebug() << "Baud Rate checkbox is checked";
        emit signal_writeCommand_CalDialog(ui->deviceModel_CalDialog->text(), "writeBaudRate", ui->baudNum_CalDialog->currentText());
        break;
    case 5:
        qDebug() << "Device Address checkbox is checked";
        break;
    case 6:
        qDebug() << "Board Card Number checkbox is checked";
        // 检查输入是否为空且长度必须为10个字符
        if (ui->boardCardNumberEdit_CalDialog->text().isEmpty())
        {
            QMessageBox::warning(this, "输入错误", "板卡编号输入不能为空，请输入有效的板卡编号。");
        }
        else if (ui->boardCardNumberEdit_CalDialog->text().length() != 10)
        {
            QMessageBox::warning(this, "输入错误", "板卡编号必须为10个字符。");
        }
        else
        {
            emit signal_writeCommand_CalDialog(ui->deviceModel_CalDialog->text(), "writeBoardCardNumber", ui->boardCardNumberEdit_CalDialog->text());
        }
        break;
    case 7:
        QMessageBox::warning(this, "错误", "无法写入板卡精度。");
        break;
    case 8:
        qDebug() << "1618A Filter Frequency checkbox is checked";
        if (ui->filterNum_CalDialog_1618A->text().isEmpty())
        {
            QMessageBox::warning(this, "输入错误", "滤波次数输入不能为空，请输入有效的滤波次数。");
        }
        else
        {
            emit signal_writeCommand_CalDialog(ui->deviceModel_CalDialog->text(), "writeFilterNum1618A", ui->filterNum_CalDialog_1618A->text()); // 设备型号、功能码、写入值
        }
        break;
    default:
        qDebug() << "No checkbox is checked";
        QMessageBox::warning(this, "提示", "请勾选至少一个系统参数");
        break;
    }
}

void CalibrationDialog::on_init_CalDialog_clicked()
{
    emit signal_writeCommand_CalDialog(ui->deviceModel_CalDialog->text(), "writeInit", "");
}

void CalibrationDialog::on_reset_CalDialog_clicked()
{
    int index = ui->chs_CalDialog->currentIndex();

    QString m_resetWriteVal = QString("%1 00").arg(index + 1, 2, 16, QChar('0')).toUpper();

    // emit signal_writeCommand_CalDialog(ui->deviceModel_CalDialog->text(), "writeChannelRest", m_resetWriteVal);
    emit signal_writeCommand_CalDialog_Reset(ui->deviceModel_CalDialog->text(), "writeChannelRest", m_resetWriteVal, ui->refers_CalDialog->currentText());
}

void CalibrationDialog::on_abortButton_clicked()
{
    // emit signal_startCal_CalDialog(ui->deviceModel_CalDialog->text(), "endCal", ui->chs_CalDialog->count(), ui->refers_CalDialog->currentText());
}

void CalibrationDialog::on_startCal_CalDialog_clicked()
{
    // 判断参考是否为空等
    if (ui->refers_CalDialog->count() == 0)
    {
        QMessageBox::warning(this, "警告", "当前未勾选参考电阻，请至少选择一个参考电阻。");
        return;
    }

    if (ui->deviceModel_CalDialog->text() == "1618A-N" || ui->deviceModel_CalDialog->text() == "1618A-L")
    {
        if (ui->boardCardNumberEdit_CalDialog->text().isEmpty() || ui->boardCardModelEdit_CalDialog->text().isEmpty())
        {
            QMessageBox::warning(this, "警告", "请先读取板卡编号和板卡精度。");
            return;
        }
        QString cardModel = ui->boardCardNumberEdit_CalDialog->text();        // 板卡编号（序列号）
        QString cardType_Accuracy = ui->boardCardModelEdit_CalDialog->text(); // 板卡类型+精度

        // 开始标定 需要传回：设备型号 BK-NTC2501 - NTC-01  、通道个数
        // 格式：1618A-N-NTC-01 或 1618A-L-NTC-01
        emit signal_startCal_CalDialog(ui->deviceModel_CalDialog->text() + "-" + cardType_Accuracy, "startCal", ui->chs_CalDialog->count(), ui->refers_CalDialog->currentText());
    }
    else
    {
        if (ui->serialNumEdit_Cal->text().isEmpty())
        {
            QMessageBox::warning(this, "警告", "请先执行系统参数校准。");
            return;
        }
        // 开始标定前 限制其执行读取设备序列号等操作 便于后续生成项目历史记录用
        // 开始标定 需要传回：设备型号、通道个数
        emit signal_startCal_CalDialog(ui->deviceModel_CalDialog->text(), "startCal", ui->chs_CalDialog->count(), ui->refers_CalDialog->currentText());
    }
}

void CalibrationDialog::initCalibrationDialog(const QString &deviceModel, int chNums, const QStringList &refers)
{
    ui->baudNum_CalDialog->clear();
    QStringList baudRates = {"1200", "2400", "4800", "9600", "19200", "38400", "57600", "115200"};
    ui->baudNum_CalDialog->addItems(baudRates);

    ui->deviceModel_CalDialog->clear();
    ui->deviceModel_CalDialog->setText(deviceModel);

    ui->chs_CalDialog->clear();
    QStringList channels;
    for (int i = 1; i <= chNums; ++i)
    {
        channels << QString::number(i);
    }
    ui->chs_CalDialog->addItems(channels);
    ui->chs_CalDialog->setMaxVisibleItems(4);

    ui->refers_CalDialog->clear();
    ui->refers_CalDialog->addItems(refers);

    // 设置标定按钮状态
    if (deviceModel != "ZCLOG 334NTC")
    {
        ui->btnStartCal->setEnabled(false);
        ui->btnEndCal->setEnabled(false);
    }
    else
    {
        ui->btnStartCal->setEnabled(true);
        ui->btnEndCal->setEnabled(true);
    }

    // 只有在非1618A型号时才重置为电阻校准模式
    // 1618A型号应该根据读取的板卡精度来决定校准模式
    if (!deviceModel.contains("1618A"))
    {
        resetToResistanceCalibrationMode();
    }

    // 使用统一的按钮状态管理函数
    updateButtonStates(deviceModel);
}

// 读取序列号返回处理
void CalibrationDialog::on_signal_readCommand_CalDialog_Result(const QString &readResultData, const QString &featureCode)
{
    if (featureCode == "readNum")
    {
        ui->serialNumEdit_Cal->clear();
        ui->serialNumEdit_Cal->setText(readResultData);
    }
    else if (featureCode == "readFilterNum")
    {
        ui->filterNum_CalDialog->clear();
        ui->filterNum_CalDialog->setText(readResultData);
    }
    else if (featureCode == "readFilterNum1618A")
    {
        ui->filterNum_CalDialog_1618A->clear();
        ui->filterNum_CalDialog_1618A->setText(readResultData);
    }
    else if (featureCode == "readBoardCardNumber")
    {
        ui->boardCardNumberEdit_CalDialog->clear();
        ui->boardCardNumberEdit_CalDialog->setText(readResultData);
    }
    else if (featureCode == "readBoardCardModel")
    {
        ui->boardCardModelEdit_CalDialog->clear();
        ui->boardCardModelEdit_CalDialog->setText(readResultData);

        // 检测TC类型并更新校准按钮状态
        updateCalibrationButtonStates(readResultData);
    }
}

void CalibrationDialog::on_signal_writeCommand_CalDialog_Result(const QString &result, const QString &featureCode)
{
}

void CalibrationDialog::on_signal_startCal_CalDialog_Result(const QString &result, const QString &featureCode)
{
}

void CalibrationDialog::on_signal_startVoltageCal_CalDialog_Result(const QString &result, const QString &featureCode)
{
}

void CalibrationDialog::on_startCal_CalDialog_2_clicked()
{
    // 检查设备型号
    QString deviceModel = ui->deviceModel_CalDialog->text();
    if (deviceModel != "1618A-N" && deviceModel != "1618A-L")
    {
        QMessageBox::warning(this, "警告", "电压校准仅支持1618A-N和1618A-L设备。");
        return;
    }

    // 检查板卡信息
    if (ui->boardCardNumberEdit_CalDialog->text().isEmpty() || ui->boardCardModelEdit_CalDialog->text().isEmpty())
    {
        QMessageBox::warning(this, "警告", "请先读取板卡编号和板卡精度。");
        return;
    }

    QString cardType = ui->boardCardModelEdit_CalDialog->text();
    if (!cardType.startsWith("TC-"))
    {
        QMessageBox::warning(this, "警告", "当前板卡类型不支持电压校准，请使用电阻校准。");
        return;
    }

    // 禁用按钮，启用停止按钮
    ui->startCal_CalDialog_2->setEnabled(false);
    ui->abortButton_2->setEnabled(true);

    // 发送电压校准信号
    QString fullDeviceModel = deviceModel + "-" + cardType;
    emit signal_startVoltageCal_CalDialog(fullDeviceModel, "startVoltageCal");
}

void CalibrationDialog::on_abortButton_2_clicked()
{
    // 防止重复点击
    if (!ui->abortButton_2->isEnabled())
        return;

    // 恢复按钮状态
    ui->startCal_CalDialog_2->setEnabled(true);
    ui->abortButton_2->setEnabled(false);

    // 发送停止电压校准信号
    emit signal_abortVoltageCal_CalDialog();

    qDebug() << "电压校准被用户中止";
}
